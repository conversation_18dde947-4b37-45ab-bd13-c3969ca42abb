{"name": "uglify-es", "description": "JavaScript parser, mangler/compressor and beautifier toolkit for ES6+", "homepage": "https://github.com/mishoo/UglifyJS2/tree/harmony", "author": "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)", "license": "BSD-2-<PERSON><PERSON>", "version": "3.3.10", "engines": {"node": ">=0.8.0"}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)"], "repository": "https://github.com/mishoo/UglifyJS2.git#harmony", "main": "tools/node.js", "bin": {"uglifyjs": "bin/uglifyjs"}, "files": ["bin", "lib", "tools", "LICENSE"], "dependencies": {"commander": "~2.14.1", "source-map": "~0.6.1"}, "devDependencies": {"acorn": "~5.4.1", "mocha": "~3.5.1", "semver": "~5.5.0"}, "scripts": {"test": "node test/run-tests.js"}, "keywords": ["uglify", "uglify-es", "uglify-js", "minify", "minifier", "javascript", "ecmascript", "es5", "es6", "es7", "es8", "es2015", "es2016", "es2017", "async", "await"]}