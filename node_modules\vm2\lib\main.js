'use strict';

const {
	VMError
} = require('./bridge');
const {
	VMScript
} = require('./script');
const {
	VM
} = require('./vm');
const {
	NodeVM
} = require('./nodevm');
const {
	VMFileSystem
} = require('./filesystem');
const {
	Resolver
} = require('./resolver');
const {
	makeResolverFromLegacyOptions
} = require('./resolver-compat');

exports.VMError = VMError;
exports.VMScript = VMScript;
exports.NodeVM = NodeVM;
exports.VM = VM;
exports.VMFileSystem = VMFileSystem;
exports.Resolver = Resolver;
exports.makeResolverFromLegacyOptions = makeResolverFromLegacyOptions;
