{"name": "wxapp-unpacker", "version": "1.0.1", "description": "<PERSON><PERSON><PERSON> App(微信小程序, .wxapkg)解包及相关文件(.wxss, .json, .wxs, .wxml)还原工具", "main": "wuWxapkg.js", "repository": {"type": "git", "url": "git+https://github.com/qwerty472123/wxappUnpacker.git"}, "author": "qwerty472123", "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/qwerty472123/wxappUnpacker/issues"}, "scripts": {"pkg": "node wuWxapkg.js", "css": "node wuWxss.js", "xml": "node wuWxml.js", "js": "node wuJs.js", "cfg": "node wuConfig.js"}, "homepage": "https://github.com/qwerty472123/wxappUnpacker#readme", "dependencies": {"cheerio": "^1.0.0-rc.3", "css-tree": "^1.0.0-alpha.28", "cssbeautify": "^0.3.1", "escodegen": "^1.11.0", "esprima": "^4.0.0", "js-beautify": "^1.7.5", "uglify-es": "^3.3.9", "vm2": "^3.6.0"}}